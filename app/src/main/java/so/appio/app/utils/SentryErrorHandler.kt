package so.appio.app.utils

import android.os.Build
import android.util.Log
import io.sentry.Sentry
import io.sentry.SentryLevel

/**
 * Centralized error handling utility for Sentry integration.
 * Provides consistent error reporting with appropriate context and filtering.
 */
object SentryErrorHandler {
    private const val TAG = "LOG:SentryErrorHandler"

    /**
     * Check if an exception should be ignored and not sent to Sentry.
     */
    private fun shouldIgnoreException(exception: Throwable): <PERSON><PERSON><PERSON> {
        // Ignore if SDK is below min supported. This happens during Google Play release testing
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) { // S = API 31
            return true
        }

        return false
    }

    /**
     * Global uncaught exception handler that sends crashes to Sentry.
     * This should be set early in the application lifecycle.
     */
    class GlobalExceptionHandler(
        private val defaultHandler: Thread.UncaughtExceptionHandler?
    ) : Thread.UncaughtExceptionHandler {
        
        override fun uncaughtException(thread: Thread, exception: Throwable) {
            try {
                Log.e(TAG, "Uncaught exception in thread ${thread.name}, exception ${exception.javaClass.simpleName}", exception)

                // Check if this exception should be ignored
                if (shouldIgnoreException(exception)) {
                    Log.d(TAG, "Ignoring known exception: ${exception.javaClass.simpleName} - ${exception.message}")
                } else {
                    // Send to Sentry with additional context
                    Sentry.captureException(exception) { scope ->
                        scope.setTag("crash.thread", thread.name)
                        scope.setTag("crash.type", "uncaught_exception")
                        scope.setLevel(SentryLevel.FATAL)
                        scope.setExtra("thread.id", thread.id.toString())
                        scope.setExtra("thread.state", thread.state.toString())
                    }

                    Log.d(TAG, "Uncaught exception sent to Sentry")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send uncaught exception to Sentry", e)
            }

            // Call the original handler to maintain normal crash behavior
            defaultHandler?.uncaughtException(thread, exception)
        }
    }

    /**
     * Install the global exception handler.
     * Call this early in Application.onCreate()
     */
    fun installGlobalExceptionHandler() {
        try {
            val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            Thread.setDefaultUncaughtExceptionHandler(GlobalExceptionHandler(defaultHandler))
            Log.d(TAG, "Global exception handler installed")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to install global exception handler", e)
        }
    }

    /**
     * Report an API error to Sentry with relevant context.
     */
    fun reportApiError(
        exception: Throwable,
        method: String? = null,
        url: String? = null,
        statusCode: Int? = null,
        responseBody: String? = null
    ) {
        try {
            Sentry.captureException(exception) { scope ->
                scope.setTag("error.type", "api_error")
                scope.setTag("error.category", "network")

                method?.let { scope.setExtra("api.method", it) }
                url?.let { scope.setExtra("api.url", it) }
                statusCode?.let { scope.setExtra("api.status_code", it.toString()) }
                responseBody?.let {
                    // Limit response body to avoid huge payloads
                    val truncatedBody = if (it.length > 1000) "${it.take(1000)}..." else it
                    scope.setExtra("api.response_body", truncatedBody)
                }

                scope.setLevel(SentryLevel.ERROR)
            }
            
            Log.d(TAG, "API error sent to Sentry: ${exception.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send API error to Sentry", e)
        }
    }

    /**
     * Report a database error to Sentry with relevant context.
     */
    fun reportDatabaseError(
        exception: Throwable,
        operation: String? = null,
        table: String? = null,
        entityId: String? = null
    ) {
        try {
            Sentry.captureException(exception) { scope ->
                scope.setTag("error.type", "database_error")
                scope.setTag("error.category", "data")

                operation?.let { scope.setExtra("db.operation", it) }
                table?.let { scope.setExtra("db.table", it) }
                entityId?.let { scope.setExtra("db.entity_id", it) }

                scope.setLevel(SentryLevel.ERROR)
            }
            
            Log.d(TAG, "Database error sent to Sentry: ${exception.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send database error to Sentry", e)
        }
    }

    /**
     * Report a widget error to Sentry with relevant context.
     */
    fun reportWidgetError(
        exception: Throwable,
        widgetId: Int? = null,
        serviceId: String? = null,
        operation: String? = null
    ) {
        try {
            Sentry.captureException(exception) { scope ->
                scope.setTag("error.type", "widget_error")
                scope.setTag("error.category", "ui")

                widgetId?.let { scope.setExtra("widget.id", it.toString()) }
                serviceId?.let { scope.setExtra("widget.service_id", it) }
                operation?.let { scope.setExtra("widget.operation", it) }

                scope.setLevel(SentryLevel.ERROR)
            }
            
            Log.d(TAG, "Widget error sent to Sentry: ${exception.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send widget error to Sentry", e)
        }
    }

    /**
     * Report a Firebase/FCM error to Sentry with relevant context.
     */
    fun reportFirebaseError(
        exception: Throwable,
        operation: String? = null,
        messageId: String? = null
    ) {
        try {
            Sentry.captureException(exception) { scope ->
                scope.setTag("error.type", "firebase_error")
                scope.setTag("error.category", "messaging")

                operation?.let { scope.setExtra("firebase.operation", it) }
                messageId?.let { scope.setExtra("firebase.message_id", it) }

                scope.setLevel(SentryLevel.ERROR)
            }
            
            Log.d(TAG, "Firebase error sent to Sentry: ${exception.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send Firebase error to Sentry", e)
        }
    }

    /**
     * Report a general application error to Sentry with basic context.
     */
    fun reportError(
        exception: Throwable,
        component: String? = null,
        operation: String? = null,
        level: SentryLevel = SentryLevel.ERROR
    ) {
        try {
            Sentry.captureException(exception) { scope ->
                scope.setTag("error.type", "app_error")
                scope.setTag("error.category", "general")

                component?.let { scope.setExtra("app.component", it) }
                operation?.let { scope.setExtra("app.operation", it) }

                scope.setLevel(level)
            }
            
            Log.d(TAG, "Application error sent to Sentry: ${exception.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send application error to Sentry", e)
        }
    }

    /**
     * Report a warning-level issue to Sentry.
     */
    fun reportWarning(
        message: String,
        component: String? = null,
        context: Map<String, String>? = null
    ) {
        try {
            Sentry.captureMessage(message) { scope ->
                scope.setTag("warning.type", "app_warning")
                scope.setLevel(SentryLevel.WARNING)

                component?.let { scope.setExtra("app.component", it) }
                context?.forEach { (key, value) ->
                    scope.setExtra("context.$key", value)
                }
            }
            
            Log.d(TAG, "Warning sent to Sentry: $message")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send warning to Sentry", e)
        }
    }
}
